import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { HistoryState, ScanResult } from './types';
import {
  STORAGE_KEYS,
  storeData,
  getData,
  storeAuthAwareData,
  getAuthAwareData
} from './storage';
import { useAuth } from './AuthContext';
import { useScan } from './ScanContext';
import { isDuplicateScan } from './utils/duplicateDetection';

// Default history state
const defaultHistoryState: HistoryState = {
  scans: [],
  isLoading: false,
  error: null,
};

// Create the context
export const HistoryContext = createContext<{
  historyState: HistoryState;
  addScanToHistory: (scan: ScanResult) => Promise<void>;
  removeScanFromHistory: (scanId: string) => Promise<void>;
  removeMultipleScansFromHistory: (scanIds: string[]) => Promise<void>;
  clearHistory: () => Promise<void>;
  clearError: () => void;
}>({
  historyState: defaultHistoryState,
  addScanToHistory: async () => {},
  removeScanFromHistory: async () => {},
  removeMultipleScansFromHistory: async () => {},
  clearHistory: async () => {},
  clearError: () => {},
});

// Custom hook to use the history context
export const useHistory = () => useContext(HistoryContext);

// Provider component
export const HistoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [historyState, setHistoryState] = useState<HistoryState>(defaultHistoryState);
  const { authState } = useAuth();
  const { scanState } = useScan();

  // Generate a unique ID for scan results - memoized
  const generateUniqueId = useCallback(() => {
    return `scan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }, []);

  // Helper function to check if a scan already exists in history (using utility function)
  const checkDuplicateScan = useCallback((newScan: ScanResult, existingScans: ScanResult[]): boolean => {
    return isDuplicateScan(newScan, existingScans);
  }, []);

  // Load history from storage on mount and when auth state changes
  useEffect(() => {
    const loadHistory = async () => {
      try {
        setHistoryState({ ...historyState, isLoading: true });

        // Use authentication-aware storage
        const storedHistory = await getAuthAwareData<HistoryState>(
          authState.isAuthenticated,
          STORAGE_KEYS.ANONYMOUS.HISTORY,
          STORAGE_KEYS.AUTHENTICATED.HISTORY
        );

        if (storedHistory) {
          // Simply load the stored history without any duplicate checking
          setHistoryState({
            ...storedHistory,
            isLoading: false,
          });
        } else {
          setHistoryState({
            ...defaultHistoryState,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Error loading history:', error);
        setHistoryState({
          ...defaultHistoryState,
          isLoading: false,
          error: 'Failed to load scan history.',
        });
      }
    };

    loadHistory();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Add scan to history function - memoized with duplicate replacement
  const addScanToHistory = useCallback(async (scan: ScanResult): Promise<void> => {
    try {
      // Check if this scan already exists in history
      const existingDuplicateIndex = historyState.scans.findIndex(existingScan => {
        // Check for duplicate based on product name (case-insensitive)
        return existingScan.productName.toLowerCase() === scan.productName.toLowerCase();
      });

      let updatedScans: ScanResult[];

      if (existingDuplicateIndex !== -1) {
        // Remove the old scan and add the new one
        // console.log(`Replacing existing scan for product "${scan.productName}" with new scan`);
        updatedScans = [...historyState.scans];
        updatedScans.splice(existingDuplicateIndex, 1); // Remove old scan
        updatedScans.unshift(scan); // Add new scan at the beginning
      } else {
        // No duplicate found, add normally
        // console.log(`Adding new scan for product: ${scan.productName}`);
        updatedScans = [scan, ...historyState.scans];
      }

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error adding scan to history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to add scan to history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Remove scan from history function - memoized
  const removeScanFromHistory = useCallback(async (scanId: string): Promise<void> => {
    try {
      // Remove scan from history
      const updatedScans = historyState.scans.filter(scan => scan.id !== scanId);

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error removing scan from history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to remove scan from history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Remove multiple scans from history function - memoized
  const removeMultipleScansFromHistory = useCallback(async (scanIds: string[]): Promise<void> => {
    try {
      // Remove all specified scans from history in one operation
      const updatedScans = historyState.scans.filter(scan => !scanIds.includes(scan.id));

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error removing multiple scans from history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to remove scans from history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Clear history function - memoized
  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      const newState: HistoryState = {
        scans: [],
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error clearing history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to clear history.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Clear error function - memoized
  const clearError = useCallback(() => {
    setHistoryState(prevState => ({ ...prevState, error: null }));
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    historyState,
    addScanToHistory,
    removeScanFromHistory,
    removeMultipleScansFromHistory,
    clearHistory,
    clearError,
  }), [historyState, addScanToHistory, removeScanFromHistory, removeMultipleScansFromHistory, clearHistory, clearError]);

  return (
    <HistoryContext.Provider value={contextValue}>
      {children}
    </HistoryContext.Provider>
  );
};
