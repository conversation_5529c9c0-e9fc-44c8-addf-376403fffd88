import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { ScanState, ScanResult } from './types';
import {
  STORAGE_KEYS,
  storeData,
  getData,
  storeAuthAwareData,
  getAuthAwareData
} from './storage';
import { useAuth } from './AuthContext';

// Default scan state
const defaultScanState: ScanState = {
  currentScan: null,
  isScanning: false,
  error: null,
};

// Create the context
export const ScanContext = createContext<{
  scanState: ScanState;
  startScan: () => void;
  completeScan: (scanResult: ScanResult) => Promise<void>;
  clearCurrentScan: () => Promise<void>;
  clearError: () => void;
}>({
  scanState: defaultScanState,
  startScan: () => {},
  completeScan: async () => {},
  clearCurrentScan: async () => {},
  clearError: () => {},
});

// Custom hook to use the scan context
export const useScan = () => useContext(ScanContext);

// Provider component
export const ScanProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [scanState, setScanState] = useState<ScanState>(defaultScanState);
  const { authState } = useAuth();

  // Load current scan from storage on mount and when auth state changes
  useEffect(() => {
    const loadCurrentScan = async () => {
      try {
        // Use authentication-aware storage
        const storedScan = await getAuthAwareData<ScanState>(
          authState.isAuthenticated,
          STORAGE_KEYS.ANONYMOUS.CURRENT_SCAN,
          STORAGE_KEYS.AUTHENTICATED.CURRENT_SCAN
        );

        if (storedScan) {
          setScanState(storedScan);
        } else {
          // If no stored scan, reset to default state
          setScanState(defaultScanState);
        }
      } catch (error) {
        console.error('Error loading current scan:', error);
        // Reset to default state on error
        setScanState(defaultScanState);
      }
    };

    loadCurrentScan();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Start scan function - memoized
  const startScan = useCallback(() => {
    setScanState(prevState => ({
      ...prevState,
      isScanning: true,
      error: null,
    }));
  }, []);

  // Complete scan function - memoized
  const completeScan = useCallback(async (scanResult: ScanResult): Promise<void> => {
    try {
      const newState: ScanState = {
        currentScan: scanResult,
        isScanning: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.CURRENT_SCAN,
        STORAGE_KEYS.AUTHENTICATED.CURRENT_SCAN,
        newState
      );

      // Update state
      setScanState(newState);
    } catch (error) {
      console.error('Error completing scan:', error);
      setScanState(prevState => ({
        ...prevState,
        isScanning: false,
        error: 'Failed to save scan result. Please try again.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Clear current scan function - memoized
  const clearCurrentScan = useCallback(async (): Promise<void> => {
    try {
      const newState: ScanState = {
        currentScan: null,
        isScanning: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.CURRENT_SCAN,
        STORAGE_KEYS.AUTHENTICATED.CURRENT_SCAN,
        newState
      );

      // Update state
      setScanState(newState);
    } catch (error) {
      console.error('Error clearing scan:', error);
      setScanState(prevState => ({
        ...prevState,
        error: 'Failed to clear scan. Please try again.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Clear error function - memoized
  const clearError = useCallback(() => {
    setScanState(prevState => ({ ...prevState, error: null }));
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    scanState,
    startScan,
    completeScan,
    clearCurrentScan,
    clearError,
  }), [scanState, startScan, completeScan, clearCurrentScan, clearError]);

  return (
    <ScanContext.Provider value={contextValue}>
      {children}
    </ScanContext.Provider>
  );
};
